import React from 'react';
import { View, Text, ScrollView } from 'react-native';

export default {
  title: 'Theme/Overview',
  component: View,
};

const ColorGrid = ({ title, colors }: { title: string; colors: string[] }) => (
  <View className="mb-6">
    <Text className="text-typography-900 font-semibold text-sm mb-3">{title}</Text>
    <View className="flex-row flex-wrap gap-2">
      {colors.map((color, index) => (
        <View key={index} className={`w-8 h-8 rounded ${color} border border-outline-200`} />
      ))}
    </View>
  </View>
);

const TypographyScale = () => (
  <View className="mb-6">
    <Text className="text-typography-900 font-semibold text-sm mb-3">Typography Scale</Text>
    <View className="bg-background-50 p-4 rounded-lg">
      <Text className="text-6xl text-typography-900 font-bold mb-2">Aa</Text>
      <Text className="text-4xl text-typography-800 font-semibold mb-2">Heading 1</Text>
      <Text className="text-2xl text-typography-800 font-semibold mb-2">Heading 2</Text>
      <Text className="text-xl text-typography-800 font-medium mb-2">Heading 3</Text>
      <Text className="text-lg text-typography-700 mb-2">Large Text</Text>
      <Text className="text-base text-typography-700 mb-2">Body Text</Text>
      <Text className="text-sm text-typography-600 mb-2">Small Text</Text>
      <Text className="text-xs text-typography-500">Caption Text</Text>
    </View>
  </View>
);

const SpacingScale = () => (
  <View className="mb-6">
    <Text className="text-typography-900 font-semibold text-sm mb-3">Spacing Scale</Text>
    <View className="bg-background-50 p-4 rounded-lg">
      {[1, 2, 3, 4, 6, 8, 12, 16, 20, 24].map((size) => (
        <View key={size} className="flex-row items-center mb-2">
          <Text className="text-typography-600 text-xs w-12">{size}</Text>
          <View className={`bg-primary-500 h-4 p-${size}`} style={{ width: size * 4 }} />
        </View>
      ))}
    </View>
  </View>
);

const BorderRadiusScale = () => (
  <View className="mb-6">
    <Text className="text-typography-900 font-semibold text-sm mb-3">Border Radius</Text>
    <View className="bg-background-50 p-4 rounded-lg">
      <View className="flex-row flex-wrap gap-4">
        <View className="bg-primary-500 w-16 h-16 rounded-none items-center justify-center">
          <Text className="text-typography-white text-xs">none</Text>
        </View>
        <View className="bg-primary-500 w-16 h-16 rounded-sm items-center justify-center">
          <Text className="text-typography-white text-xs">sm</Text>
        </View>
        <View className="bg-primary-500 w-16 h-16 rounded-md items-center justify-center">
          <Text className="text-typography-white text-xs">md</Text>
        </View>
        <View className="bg-primary-500 w-16 h-16 rounded-lg items-center justify-center">
          <Text className="text-typography-white text-xs">lg</Text>
        </View>
        <View className="bg-primary-500 w-16 h-16 rounded-xl items-center justify-center">
          <Text className="text-typography-white text-xs">xl</Text>
        </View>
        <View className="bg-primary-500 w-16 h-16 rounded-full items-center justify-center">
          <Text className="text-typography-white text-xs">full</Text>
        </View>
      </View>
    </View>
  </View>
);

const ShadowScale = () => (
  <View className="mb-6">
    <Text className="text-typography-900 font-semibold text-sm mb-3">Shadow Scale</Text>
    <View className="bg-background-50 p-6 rounded-lg">
      <View className="flex-row flex-wrap gap-4">
        <View className="bg-background-0 p-4 rounded-lg shadow-soft-1">
          <Text className="text-typography-700 text-xs text-center">soft-1</Text>
        </View>
        <View className="bg-background-0 p-4 rounded-lg shadow-soft-2">
          <Text className="text-typography-700 text-xs text-center">soft-2</Text>
        </View>
        <View className="bg-background-0 p-4 rounded-lg shadow-soft-3">
          <Text className="text-typography-700 text-xs text-center">soft-3</Text>
        </View>
        <View className="bg-background-0 p-4 rounded-lg shadow-hard-2">
          <Text className="text-typography-700 text-xs text-center">hard-2</Text>
        </View>
      </View>
    </View>
  </View>
);

export const ThemeOverview = () => (
  <ScrollView className="flex-1 p-4 bg-background-0">
    <Text className="text-typography-900 text-3xl font-bold mb-2">Design System Overview</Text>
    <Text className="text-typography-600 text-base mb-8">
      Complete overview of the design system including colors, typography, spacing, and more.
    </Text>
    
    <ColorGrid
      title="Primary Colors"
      colors={[
        'bg-primary-100', 'bg-primary-200', 'bg-primary-300', 'bg-primary-400', 
        'bg-primary-500', 'bg-primary-600', 'bg-primary-700', 'bg-primary-800'
      ]}
    />
    
    <ColorGrid
      title="Secondary Colors"
      colors={[
        'bg-secondary-100', 'bg-secondary-200', 'bg-secondary-300', 'bg-secondary-400', 
        'bg-secondary-500', 'bg-secondary-600', 'bg-secondary-700', 'bg-secondary-800'
      ]}
    />
    
    <ColorGrid
      title="Semantic Colors"
      colors={[
        'bg-error-500', 'bg-success-500', 'bg-warning-500', 'bg-info-500'
      ]}
    />
    
    <TypographyScale />
    <SpacingScale />
    <BorderRadiusScale />
    <ShadowScale />
  </ScrollView>
);

export const ComponentExamples = () => (
  <ScrollView className="flex-1 p-4 bg-background-0">
    <Text className="text-typography-900 text-3xl font-bold mb-2">Component Examples</Text>
    <Text className="text-typography-600 text-base mb-8">
      Examples showing how theme tokens work together in real components.
    </Text>
    
    {/* Card Example */}
    <View className="mb-8">
      <Text className="text-typography-900 text-xl font-semibold mb-4">Card Component</Text>
      <View className="bg-background-0 p-6 rounded-lg shadow-soft-2 border border-outline-200">
        <Text className="text-typography-900 text-lg font-semibold mb-2">Card Title</Text>
        <Text className="text-typography-600 text-base mb-4">
          This card demonstrates the use of background colors, typography, spacing, borders, and shadows.
        </Text>
        <View className="flex-row gap-3">
          <View className="bg-primary-500 px-4 py-2 rounded-md">
            <Text className="text-typography-white font-medium">Primary</Text>
          </View>
          <View className="bg-background-0 px-4 py-2 rounded-md border border-outline-300">
            <Text className="text-typography-700 font-medium">Secondary</Text>
          </View>
        </View>
      </View>
    </View>
    
    {/* Alert Examples */}
    <View className="mb-8">
      <Text className="text-typography-900 text-xl font-semibold mb-4">Alert Components</Text>
      
      <View className="bg-error-50 p-4 rounded-lg border border-error-300 mb-3">
        <Text className="text-error-800 font-semibold mb-1">Error Alert</Text>
        <Text className="text-error-700 text-sm">Something went wrong. Please try again.</Text>
      </View>
      
      <View className="bg-success-50 p-4 rounded-lg border border-success-300 mb-3">
        <Text className="text-success-800 font-semibold mb-1">Success Alert</Text>
        <Text className="text-success-700 text-sm">Operation completed successfully!</Text>
      </View>
      
      <View className="bg-warning-50 p-4 rounded-lg border border-warning-300 mb-3">
        <Text className="text-warning-800 font-semibold mb-1">Warning Alert</Text>
        <Text className="text-warning-700 text-sm">Please review your input before proceeding.</Text>
      </View>
      
      <View className="bg-info-50 p-4 rounded-lg border border-info-300">
        <Text className="text-info-800 font-semibold mb-1">Info Alert</Text>
        <Text className="text-info-700 text-sm">Here's some helpful information for you.</Text>
      </View>
    </View>
    
    {/* Form Example */}
    <View className="mb-8">
      <Text className="text-typography-900 text-xl font-semibold mb-4">Form Components</Text>
      <View className="bg-background-50 p-6 rounded-lg">
        <Text className="text-typography-900 font-medium mb-2">Email Address</Text>
        <View className="bg-background-0 p-3 rounded-md border border-outline-300 mb-4">
          <Text className="text-typography-400">Enter your email</Text>
        </View>
        
        <Text className="text-typography-900 font-medium mb-2">Message</Text>
        <View className="bg-background-0 p-3 rounded-md border border-outline-300 h-20 mb-4">
          <Text className="text-typography-400">Type your message here...</Text>
        </View>
        
        <View className="bg-primary-500 p-3 rounded-md items-center">
          <Text className="text-typography-white font-semibold">Send Message</Text>
        </View>
      </View>
    </View>
  </ScrollView>
);

export const ThemeComparison = () => (
  <ScrollView className="flex-1 p-4 bg-background-0">
    <Text className="text-typography-900 text-3xl font-bold mb-2">Light vs Dark Theme</Text>
    <Text className="text-typography-600 text-base mb-8">
      Use the theme switcher in the toolbar to see how components adapt to different themes.
    </Text>
    
    <View className="mb-8">
      <Text className="text-typography-900 text-xl font-semibold mb-4">Theme-Aware Components</Text>
      
      <View className="bg-background-50 p-6 rounded-lg mb-4">
        <Text className="text-typography-900 text-lg font-semibold mb-2">Adaptive Card</Text>
        <Text className="text-typography-700 mb-4">
          This card automatically adapts its colors based on the current theme.
        </Text>
        <View className="flex-row gap-3">
          <View className="bg-primary-500 px-4 py-2 rounded-md">
            <Text className="text-typography-white font-medium">Primary Button</Text>
          </View>
          <View className="bg-background-0 px-4 py-2 rounded-md border border-outline-300">
            <Text className="text-typography-700 font-medium">Secondary Button</Text>
          </View>
        </View>
      </View>
      
      <View className="bg-background-100 p-6 rounded-lg">
        <Text className="text-typography-800 text-lg font-semibold mb-2">Nested Card</Text>
        <Text className="text-typography-600 mb-4">
          Notice how nested elements maintain proper contrast ratios in both themes.
        </Text>
        <View className="bg-background-0 p-4 rounded-md shadow-soft-1">
          <Text className="text-typography-900 font-medium mb-2">Inner Content</Text>
          <Text className="text-typography-600 text-sm">
            This content maintains readability in both light and dark themes.
          </Text>
        </View>
      </View>
    </View>
  </ScrollView>
);
